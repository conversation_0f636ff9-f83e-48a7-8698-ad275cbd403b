/**
 * 增强版音频播放器 - 支持多章节播放和章节导航
 */
class EnhancedAudioPlayer {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            autoPlay: false,
            showChapterList: true,
            enableKeyboardShortcuts: true,
            preloadNextChapter: true,
            ...options
        };
        
        this.currentTaskId = null;
        this.playlist = null;
        this.currentChapterIndex = 0;
        this.audio = null;
        this.isLoading = false;
        this.preloadedChapters = new Map(); // 预加载的章节缓存
        
        this.init();
    }

    init() {
        this.createPlayerHTML();
        this.bindEvents();
        
        if (this.options.enableKeyboardShortcuts) {
            this.bindKeyboardShortcuts();
        }
    }

    createPlayerHTML() {
        this.container.innerHTML = `
            <div class="enhanced-audio-player">
                <!-- 播放器头部 -->
                <div class="player-header">
                    <div class="book-info">
                        <h3 class="book-title">选择一个音频书籍</h3>
                        <p class="book-stats"></p>
                    </div>
                    <div class="player-controls">
                        <button class="btn-toggle-chapters" title="显示/隐藏章节列表">
                            <i class="fas fa-list"></i>
                        </button>
                    </div>
                </div>

                <!-- 主播放区域 -->
                <div class="player-main">
                    <!-- 章节列表 -->
                    <div class="chapters-panel ${this.options.showChapterList ? 'visible' : 'hidden'}">
                        <div class="chapters-header">
                            <h4>章节列表</h4>
                            <span class="chapters-count">0 章节</span>
                        </div>
                        <div class="chapters-list">
                            <div class="no-chapters">暂无章节</div>
                        </div>
                    </div>

                    <!-- 播放器区域 -->
                    <div class="player-area">
                        <!-- 当前章节信息 -->
                        <div class="current-chapter-info">
                            <h4 class="chapter-title">请选择章节</h4>
                            <div class="chapter-meta">
                                <span class="chapter-index">-/-</span>
                                <span class="chapter-duration">--:--</span>
                                <span class="chapter-size">-- MB</span>
                            </div>
                        </div>

                        <!-- 音频控件 -->
                        <div class="audio-controls">
                            <audio controls preload="none" class="audio-element">
                                您的浏览器不支持音频播放。
                            </audio>
                        </div>

                        <!-- 播放进度和控制 -->
                        <div class="playback-controls">
                            <button class="btn-prev-chapter" title="上一章节" disabled>
                                <i class="fas fa-step-backward"></i>
                            </button>
                            <button class="btn-play-pause" title="播放/暂停">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn-next-chapter" title="下一章节" disabled>
                                <i class="fas fa-step-forward"></i>
                            </button>
                            
                            <div class="playback-speed">
                                <label>播放速度:</label>
                                <select class="speed-selector">
                                    <option value="0.5">0.5x</option>
                                    <option value="0.75">0.75x</option>
                                    <option value="1" selected>1x</option>
                                    <option value="1.25">1.25x</option>
                                    <option value="1.5">1.5x</option>
                                    <option value="2">2x</option>
                                </select>
                            </div>
                        </div>

                        <!-- 加载状态 -->
                        <div class="loading-indicator hidden">
                            <div class="spinner"></div>
                            <span>加载中...</span>
                        </div>
                    </div>
                </div>

                <!-- 播放器底部 -->
                <div class="player-footer">
                    <div class="progress-info">
                        <span class="current-time">00:00</span>
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <span class="total-time">00:00</span>
                    </div>
                </div>
            </div>
        `;

        // 获取DOM元素引用
        this.elements = {
            bookTitle: this.container.querySelector('.book-title'),
            bookStats: this.container.querySelector('.book-stats'),
            chaptersPanel: this.container.querySelector('.chapters-panel'),
            chaptersList: this.container.querySelector('.chapters-list'),
            chaptersCount: this.container.querySelector('.chapters-count'),
            chapterTitle: this.container.querySelector('.chapter-title'),
            chapterMeta: this.container.querySelector('.chapter-meta'),
            audio: this.container.querySelector('.audio-element'),
            btnToggleChapters: this.container.querySelector('.btn-toggle-chapters'),
            btnPrevChapter: this.container.querySelector('.btn-prev-chapter'),
            btnPlayPause: this.container.querySelector('.btn-play-pause'),
            btnNextChapter: this.container.querySelector('.btn-next-chapter'),
            speedSelector: this.container.querySelector('.speed-selector'),
            loadingIndicator: this.container.querySelector('.loading-indicator'),
            currentTime: this.container.querySelector('.current-time'),
            totalTime: this.container.querySelector('.total-time'),
            progressBar: this.container.querySelector('.progress-bar'),
            progressFill: this.container.querySelector('.progress-fill')
        };

        this.audio = this.elements.audio;
    }

    bindEvents() {
        // 章节列表切换
        this.elements.btnToggleChapters.addEventListener('click', () => {
            this.toggleChaptersList();
        });

        // 播放控制
        this.elements.btnPlayPause.addEventListener('click', () => {
            this.togglePlayPause();
        });

        this.elements.btnPrevChapter.addEventListener('click', () => {
            this.playPreviousChapter();
        });

        this.elements.btnNextChapter.addEventListener('click', () => {
            this.playNextChapter();
        });

        // 播放速度控制
        this.elements.speedSelector.addEventListener('change', (e) => {
            this.setPlaybackSpeed(parseFloat(e.target.value));
        });

        // 音频事件
        this.audio.addEventListener('loadstart', () => this.showLoading());
        this.audio.addEventListener('canplay', () => this.hideLoading());
        this.audio.addEventListener('play', () => this.updatePlayPauseButton(true));
        this.audio.addEventListener('pause', () => this.updatePlayPauseButton(false));
        this.audio.addEventListener('ended', () => this.onChapterEnded());
        this.audio.addEventListener('timeupdate', () => this.updateProgress());
        this.audio.addEventListener('error', (e) => this.onAudioError(e));

        // 进度条点击
        this.elements.progressBar.addEventListener('click', (e) => {
            this.seekToPosition(e);
        });
    }

    bindKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // 只在播放器获得焦点时响应快捷键
            if (!this.container.contains(document.activeElement) && 
                document.activeElement !== document.body) {
                return;
            }

            switch (e.code) {
                case 'Space':
                    e.preventDefault();
                    this.togglePlayPause();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.seekRelative(-10); // 后退10秒
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    this.seekRelative(10); // 前进10秒
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.playPreviousChapter();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    this.playNextChapter();
                    break;
            }
        });
    }

    async loadBook(taskId) {
        if (this.isLoading) return;
        
        try {
            this.isLoading = true;
            this.showLoading();
            this.currentTaskId = taskId;

            // 获取播放列表
            const playlist = await this.fetchPlaylist(taskId);
            if (!playlist) {
                throw new Error('无法获取播放列表');
            }

            this.playlist = playlist;
            this.currentChapterIndex = 0;

            // 更新UI
            this.updateBookInfo();
            this.renderChaptersList();
            this.updateNavigationButtons();

            // 加载第一章节
            if (playlist.chapters.length > 0) {
                await this.loadChapter(0);
                
                // 预加载下一章节
                if (this.options.preloadNextChapter && playlist.chapters.length > 1) {
                    this.preloadChapter(1);
                }
            }

        } catch (error) {
            console.error('加载书籍失败:', error);
            this.showError('加载书籍失败: ' + error.message);
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    async fetchPlaylist(taskId) {
        const response = await fetch(`/api/audio/playlist/${taskId}`, {
            headers: {
                'X-User-Email': this.getUserEmail()
            }
        });

        if (!response.ok) {
            throw new Error(`获取播放列表失败: ${response.statusText}`);
        }

        return await response.json();
    }

    async loadChapter(chapterIndex) {
        if (!this.playlist || chapterIndex < 0 || chapterIndex >= this.playlist.chapters.length) {
            return;
        }

        try {
            this.showLoading();
            this.currentChapterIndex = chapterIndex;
            
            const chapter = this.playlist.chapters[chapterIndex];
            
            // 检查是否已预加载
            if (this.preloadedChapters.has(chapterIndex)) {
                const preloadedUrl = this.preloadedChapters.get(chapterIndex);
                this.audio.src = preloadedUrl;
            } else {
                // 构建章节URL
                const chapterUrl = `/api/audio/chapter/${this.currentTaskId}/${chapter.index}.mp3`;
                this.audio.src = chapterUrl;
            }

            // 更新当前章节信息
            this.updateCurrentChapterInfo();
            this.updateNavigationButtons();
            this.highlightCurrentChapter();

            // 预加载下一章节
            if (this.options.preloadNextChapter && chapterIndex + 1 < this.playlist.chapters.length) {
                this.preloadChapter(chapterIndex + 1);
            }

        } catch (error) {
            console.error('加载章节失败:', error);
            this.showError('加载章节失败: ' + error.message);
        }
    }

    async preloadChapter(chapterIndex) {
        if (!this.playlist || chapterIndex < 0 || chapterIndex >= this.playlist.chapters.length) {
            return;
        }

        if (this.preloadedChapters.has(chapterIndex)) {
            return; // 已经预加载
        }

        try {
            const chapter = this.playlist.chapters[chapterIndex];
            const chapterUrl = `/api/audio/chapter/${this.currentTaskId}/${chapter.index}.mp3`;
            
            // 创建预加载的音频元素
            const preloadAudio = new Audio();
            preloadAudio.preload = 'auto';
            preloadAudio.src = chapterUrl;
            
            // 缓存预加载的URL
            this.preloadedChapters.set(chapterIndex, chapterUrl);
            
            console.log(`预加载章节 ${chapterIndex + 1}: ${chapter.title}`);

        } catch (error) {
            console.error(`预加载章节 ${chapterIndex + 1} 失败:`, error);
        }
    }

    updateBookInfo() {
        if (!this.playlist) return;

        this.elements.bookTitle.textContent = this.playlist.title;
        this.elements.bookStats.textContent = 
            `${this.playlist.totalChapters} 章节 · ${this.formatDuration(this.playlist.totalDuration)}`;
    }

    renderChaptersList() {
        if (!this.playlist) return;

        const chaptersHtml = this.playlist.chapters.map((chapter, index) => `
            <div class="chapter-item" data-index="${index}">
                <div class="chapter-number">${chapter.index}</div>
                <div class="chapter-info">
                    <div class="chapter-name">${chapter.title}</div>
                    <div class="chapter-details">
                        ${chapter.metadata.estimatedDuration} · ${chapter.wordCount} 字符
                        ${chapter.isPartial ? ' · 分段' : ''}
                    </div>
                </div>
                <div class="chapter-actions">
                    <button class="btn-play-chapter" title="播放此章节">
                        <i class="fas fa-play"></i>
                    </button>
                </div>
            </div>
        `).join('');

        this.elements.chaptersList.innerHTML = chaptersHtml;
        this.elements.chaptersCount.textContent = `${this.playlist.chapters.length} 章节`;

        // 绑定章节点击事件
        this.elements.chaptersList.addEventListener('click', (e) => {
            const chapterItem = e.target.closest('.chapter-item');
            if (chapterItem) {
                const index = parseInt(chapterItem.dataset.index);
                this.loadChapter(index);
                
                if (this.options.autoPlay) {
                    this.audio.play();
                }
            }
        });
    }

    updateCurrentChapterInfo() {
        if (!this.playlist || this.currentChapterIndex < 0) return;

        const chapter = this.playlist.chapters[this.currentChapterIndex];
        this.elements.chapterTitle.textContent = chapter.title;
        
        const chapterMeta = this.elements.chapterMeta;
        chapterMeta.innerHTML = `
            <span class="chapter-index">${this.currentChapterIndex + 1}/${this.playlist.chapters.length}</span>
            <span class="chapter-duration">${chapter.metadata.estimatedDuration}</span>
            <span class="chapter-size">${Math.ceil(chapter.wordCount / 1000)}k 字符</span>
        `;
    }

    updateNavigationButtons() {
        if (!this.playlist) return;

        this.elements.btnPrevChapter.disabled = this.currentChapterIndex <= 0;
        this.elements.btnNextChapter.disabled = this.currentChapterIndex >= this.playlist.chapters.length - 1;
    }

    highlightCurrentChapter() {
        // 移除之前的高亮
        this.elements.chaptersList.querySelectorAll('.chapter-item').forEach(item => {
            item.classList.remove('active');
        });

        // 高亮当前章节
        const currentItem = this.elements.chaptersList.querySelector(`[data-index="${this.currentChapterIndex}"]`);
        if (currentItem) {
            currentItem.classList.add('active');
            currentItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    toggleChaptersList() {
        this.elements.chaptersPanel.classList.toggle('visible');
        this.elements.chaptersPanel.classList.toggle('hidden');
    }

    togglePlayPause() {
        if (!this.audio.src) return;

        if (this.audio.paused) {
            this.audio.play();
        } else {
            this.audio.pause();
        }
    }

    playPreviousChapter() {
        if (this.currentChapterIndex > 0) {
            this.loadChapter(this.currentChapterIndex - 1);
            if (!this.audio.paused) {
                this.audio.play();
            }
        }
    }

    playNextChapter() {
        if (this.currentChapterIndex < this.playlist.chapters.length - 1) {
            this.loadChapter(this.currentChapterIndex + 1);
            if (!this.audio.paused) {
                this.audio.play();
            }
        }
    }

    setPlaybackSpeed(speed) {
        this.audio.playbackRate = speed;
    }

    seekToPosition(e) {
        if (!this.audio.duration) return;

        const rect = this.elements.progressBar.getBoundingClientRect();
        const percent = (e.clientX - rect.left) / rect.width;
        const newTime = percent * this.audio.duration;
        
        this.audio.currentTime = newTime;
    }

    seekRelative(seconds) {
        if (!this.audio.duration) return;
        
        const newTime = Math.max(0, Math.min(this.audio.duration, this.audio.currentTime + seconds));
        this.audio.currentTime = newTime;
    }

    updateProgress() {
        if (!this.audio.duration) return;

        const percent = (this.audio.currentTime / this.audio.duration) * 100;
        this.elements.progressFill.style.width = `${percent}%`;
        
        this.elements.currentTime.textContent = this.formatTime(this.audio.currentTime);
        this.elements.totalTime.textContent = this.formatTime(this.audio.duration);
    }

    updatePlayPauseButton(isPlaying) {
        const icon = this.elements.btnPlayPause.querySelector('i');
        if (isPlaying) {
            icon.className = 'fas fa-pause';
            this.elements.btnPlayPause.title = '暂停';
        } else {
            icon.className = 'fas fa-play';
            this.elements.btnPlayPause.title = '播放';
        }
    }

    onChapterEnded() {
        // 自动播放下一章节
        if (this.currentChapterIndex < this.playlist.chapters.length - 1) {
            this.playNextChapter();
        } else {
            // 播放完毕
            this.updatePlayPauseButton(false);
            console.log('整本书播放完毕');
        }
    }

    onAudioError(e) {
        console.error('音频播放错误:', e);
        this.showError('音频播放出错，请重试');
    }

    showLoading() {
        this.elements.loadingIndicator.classList.remove('hidden');
    }

    hideLoading() {
        this.elements.loadingIndicator.classList.add('hidden');
    }

    showError(message) {
        // 可以实现一个错误提示组件
        alert(message);
    }

    formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    formatDuration(seconds) {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        
        if (hours > 0) {
            return `${hours}小时${minutes}分钟`;
        } else {
            return `${minutes}分钟`;
        }
    }

    getUserEmail() {
        // 从localStorage获取用户数据
        const userData = localStorage.getItem('audiobook_user');
        if (userData) {
            try {
                const user = JSON.parse(userData);
                return user.email || '';
            } catch (error) {
                console.error('解析用户数据失败:', error);
                return '';
            }
        }
        return '';
    }

    // 公共API方法
    play() {
        this.audio.play();
    }

    pause() {
        this.audio.pause();
    }

    stop() {
        this.audio.pause();
        this.audio.currentTime = 0;
    }

    setVolume(volume) {
        this.audio.volume = Math.max(0, Math.min(1, volume));
    }

    getCurrentChapter() {
        return this.playlist ? this.playlist.chapters[this.currentChapterIndex] : null;
    }

    getPlaylist() {
        return this.playlist;
    }

    destroy() {
        // 清理资源
        if (this.audio) {
            this.audio.pause();
            this.audio.src = '';
        }
        
        this.preloadedChapters.clear();
        this.container.innerHTML = '';
    }
}

// 导出到全局作用域
window.EnhancedAudioPlayer = EnhancedAudioPlayer; 