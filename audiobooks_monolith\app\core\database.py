"""
数据库配置和管理
使用SQLite作为本地数据库
"""

from sqlalchemy import create_engine, MetaData, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import os

from app.core.config import settings, get_database_path
from app.core.logging import get_logger

logger = get_logger(__name__)

# 导入所有模型以确保它们被注册
from app.models.user import User  # noqa: F401
from app.models.task import Task  # noqa: F401

# 数据库引擎配置
engine_kwargs = {
    "echo": settings.database_echo,
    "pool_pre_ping": True,
}

# SQLite特殊配置
if settings.database_url.startswith("sqlite"):
    engine_kwargs.update({
        "poolclass": StaticPool,
        "connect_args": {
            "check_same_thread": False,
            "timeout": 20,
        }
    })

# 创建数据库引擎
engine = create_engine(settings.database_url, **engine_kwargs)

# 创建会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


def get_db():
    """获取数据库会话"""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


async def init_database():
    """初始化数据库"""
    try:
        # 确保数据库目录存在
        db_path = get_database_path()
        db_dir = os.path.dirname(db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info(f"数据库初始化成功: {settings.database_url}")
        
        # 检查数据库连接
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            logger.info("数据库连接测试成功")
            
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise


def close_database():
    """关闭数据库连接"""
    try:
        engine.dispose()
        logger.info("数据库连接已关闭")
    except Exception as e:
        logger.error(f"关闭数据库连接失败: {str(e)}")


def reset_database():
    """重置数据库（删除所有表并重新创建）"""
    try:
        logger.warning("正在重置数据库...")
        Base.metadata.drop_all(bind=engine)
        Base.metadata.create_all(bind=engine)
        logger.info("数据库重置完成")
    except Exception as e:
        logger.error(f"数据库重置失败: {str(e)}")
        raise


def backup_database(backup_path: str):
    """备份数据库"""
    try:
        if settings.database_url.startswith("sqlite"):
            import shutil
            db_path = get_database_path()
            if os.path.exists(db_path):
                shutil.copy2(db_path, backup_path)
                logger.info(f"数据库备份成功: {backup_path}")
            else:
                logger.warning(f"数据库文件不存在: {db_path}")
        else:
            logger.warning("当前数据库类型不支持文件备份")
    except Exception as e:
        logger.error(f"数据库备份失败: {str(e)}")
        raise


def restore_database(backup_path: str):
    """恢复数据库"""
    try:
        if settings.database_url.startswith("sqlite"):
            import shutil
            if os.path.exists(backup_path):
                db_path = get_database_path()
                shutil.copy2(backup_path, db_path)
                logger.info(f"数据库恢复成功: {backup_path}")
            else:
                logger.error(f"备份文件不存在: {backup_path}")
        else:
            logger.warning("当前数据库类型不支持文件恢复")
    except Exception as e:
        logger.error(f"数据库恢复失败: {str(e)}")
        raise
