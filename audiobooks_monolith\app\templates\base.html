<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}有声图书生成器 - AI音频转换{% endblock %}</title>
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="{{ url_for('static', path='css/style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <header>
        <nav class="navbar">
            <div class="nav-brand">
                <i class="fas fa-headphones"></i>
                <span>有声图书生成器</span>
            </div>
            <div class="nav-menu" id="nav-menu">
                <button id="nav-home" class="nav-btn active">
                    <i class="fas fa-home"></i> 首页
                </button>
                <button id="nav-workshop" class="nav-btn">
                    <i class="fas fa-tools"></i> 创作工坊
                </button>
                <button id="nav-tasks" class="nav-btn">
                    <i class="fas fa-tasks"></i> 我的任务
                </button>
                <button id="nav-library" class="nav-btn">
                    <i class="fas fa-book-open"></i> 音频库
                </button>
            </div>
            <button id="theme-toggle" class="theme-toggle" title="切换主题">
                <i class="fas fa-moon" id="theme-icon"></i>
            </button>
            <div class="nav-user" id="nav-user">
                <button id="btn-login" class="md-button md-filled-button">登录</button>
                <button id="btn-register" class="md-button md-outlined-button">注册</button>
            </div>
            <!-- 登录后的用户信息区域 -->
            <div class="nav-user-info" id="nav-user-info" style="display: none;">
                <div class="user-info-mini">
                    <div id="user-email-mini" class="user-email"></div>
                    <button id="points-clickable" class="points points-clickable" title="查看积分详情">
                        <i class="fas fa-coins"></i>
                        <span id="user-points-mini">100</span> 积分
                        <button id="btn-refresh-points" class="refresh-points-btn" title="刷新积分">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </button>
                </div>
                <div class="user-actions-mini">
                    <button id="btn-checkin" class="md-button md-text-button">
                        <i class="fas fa-calendar-check"></i>
                        <span class="btn-text">每日签到</span>
                    </button>
                    <button id="btn-logout" class="md-button md-text-button">
                        <i class="fas fa-sign-out-alt"></i>
                        <span class="btn-text">退出</span>
                    </button>
                </div>
            </div>
        </nav>
    </header>

    <main>
        {% block content %}{% endblock %}
    </main>

    <!-- 消息提示元素 -->
    <div id="message" class="message"></div>

    <!-- 登录/注册模态框 -->
    <div id="auth-modal" class="modal" style="display: none;">
        <div class="modal-content">
            <button class="close">&times;</button>
            <div id="login-form">
                <h2>用户登录</h2>
                <form id="loginForm">
                    <div class="md-text-field">
                        <input type="email" id="login-email" placeholder="邮箱地址" required>
                    </div>
                    <div class="md-text-field">
                        <input type="password" id="login-password" placeholder="密码" required>
                    </div>
                    <button type="submit" class="md-button md-filled-button">登录</button>
                </form>
                <p>还没有账户？<a href="#" id="switch-to-register">立即注册</a></p>
            </div>
            <div id="register-form" style="display: none;">
                <h2>用户注册</h2>
                <form id="registerForm">
                    <div class="md-text-field">
                        <input type="email" id="register-email" placeholder="邮箱地址" required>
                    </div>
                    <div class="md-text-field">
                        <input type="password" id="register-password" placeholder="密码（至少6位）" required>
                    </div>
                    <div class="md-text-field">
                        <input type="password" id="register-confirm" placeholder="确认密码" required>
                    </div>
                    <button type="submit" class="md-button md-filled-button">注册</button>
                </form>
                <p>已有账户？<a href="#" id="switch-to-login">立即登录</a></p>
            </div>
        </div>
    </div>

    <!-- 基础JavaScript -->
    <script src="{{ url_for('static', path='js/app.js') }}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>
