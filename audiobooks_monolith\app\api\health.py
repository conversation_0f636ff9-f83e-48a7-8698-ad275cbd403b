"""
健康检查API路由
"""

from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
from datetime import datetime

from app.core.database import get_db
from app.core.config import settings
from app.core.logging import get_logger
from app.services.storage_service import storage_service

logger = get_logger(__name__)
router = APIRouter()


@router.get("/health")
async def health_check():
    """基础健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "电子书转音频服务",
        "version": "1.0.0"
    }


@router.get("/health/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """详细健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "service": "电子书转音频服务",
        "version": "1.0.0",
        "components": {}
    }
    
    # 检查数据库连接
    try:
        db.execute(text("SELECT 1"))
        health_status["components"]["database"] = {
            "status": "healthy",
            "type": "SQLite",
            "url": settings.database_url
        }
    except Exception as e:
        health_status["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # 检查存储系统
    try:
        storage_stats = storage_service.get_storage_stats()
        health_status["components"]["storage"] = {
            "status": "healthy",
            "type": "local_filesystem",
            "stats": storage_stats
        }
    except Exception as e:
        health_status["components"]["storage"] = {
            "status": "unhealthy",
            "error": str(e)
        }
        health_status["status"] = "unhealthy"
    
    # 检查配置
    health_status["components"]["config"] = {
        "status": "healthy",
        "data_dir": settings.data_dir,
        "upload_dir": settings.upload_dir,
        "audio_dir": settings.audio_dir,
        "max_file_size": settings.max_file_size,
        "max_users": settings.max_users
    }
    
    return health_status


@router.get("/ping")
async def ping():
    """简单的ping检查"""
    return {"message": "pong"}


@router.get("/version")
async def version():
    """版本信息"""
    return {
        "service": "电子书转音频服务",
        "version": "1.0.0",
        "architecture": "单体应用",
        "build_time": datetime.now().isoformat()
    }
