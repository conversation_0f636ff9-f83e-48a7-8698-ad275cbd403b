/* 增强版音频播放器样式 */
.enhanced-audio-player {
    max-width: 1200px;
    margin: 0 auto;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 播放器头部 */
.player-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.book-info h3 {
    margin: 0 0 4px 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.book-stats {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.player-controls .btn-toggle-chapters {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.player-controls .btn-toggle-chapters:hover {
    background: rgba(255, 255, 255, 0.3);
}

/* 主播放区域 */
.player-main {
    display: flex;
    min-height: 500px;
}

/* 章节面板 */
.chapters-panel {
    width: 350px;
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    transition: all 0.3s ease;
    overflow: hidden;
}

.chapters-panel.hidden {
    width: 0;
    border-right: none;
}

.chapters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #e9ecef;
    border-bottom: 1px solid #dee2e6;
}

.chapters-header h4 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #495057;
}

.chapters-count {
    font-size: 0.85rem;
    color: #6c757d;
}

.chapters-list {
    height: 400px;
    overflow-y: auto;
    padding: 8px 0;
}

.chapters-list::-webkit-scrollbar {
    width: 6px;
}

.chapters-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chapters-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chapter-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    cursor: pointer;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
}

.chapter-item:hover {
    background: #e9ecef;
}

.chapter-item.active {
    background: #e3f2fd;
    border-left-color: #2196f3;
}

.chapter-number {
    width: 32px;
    height: 32px;
    background: #6c757d;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    font-weight: 600;
    margin-right: 12px;
    flex-shrink: 0;
}

.chapter-item.active .chapter-number {
    background: #2196f3;
}

.chapter-info {
    flex: 1;
    min-width: 0;
}

.chapter-name {
    font-weight: 500;
    color: #212529;
    margin-bottom: 4px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chapter-details {
    font-size: 0.8rem;
    color: #6c757d;
}

.chapter-actions {
    margin-left: 8px;
}

.btn-play-chapter {
    background: none;
    border: none;
    color: #6c757d;
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: color 0.2s;
}

.btn-play-chapter:hover {
    color: #2196f3;
}

.no-chapters {
    text-align: center;
    color: #6c757d;
    padding: 40px 20px;
    font-style: italic;
}

/* 播放器区域 */
.player-area {
    flex: 1;
    padding: 24px;
    display: flex;
    flex-direction: column;
}

/* 当前章节信息 */
.current-chapter-info {
    margin-bottom: 24px;
}

.chapter-title {
    margin: 0 0 8px 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: #212529;
}

.chapter-meta {
    display: flex;
    gap: 16px;
    font-size: 0.9rem;
    color: #6c757d;
}

.chapter-meta span {
    display: flex;
    align-items: center;
    gap: 4px;
}

/* 音频控件 */
.audio-controls {
    margin-bottom: 24px;
}

.audio-element {
    width: 100%;
    height: 54px;
    border-radius: 8px;
}

/* 播放控制 */
.playback-controls {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;
    flex-wrap: wrap;
}

.playback-controls button {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s;
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 48px;
    height: 48px;
}

.playback-controls button:hover:not(:disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
}

.playback-controls button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-play-pause {
    background: #2196f3 !important;
    color: white !important;
    border-color: #2196f3 !important;
}

.btn-play-pause:hover:not(:disabled) {
    background: #1976d2 !important;
    border-color: #1976d2 !important;
}

.playback-speed {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.playback-speed label {
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.speed-selector {
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background: white;
    font-size: 0.9rem;
    cursor: pointer;
}

/* 加载指示器 */
.loading-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 20px;
    color: #6c757d;
    font-size: 0.9rem;
}

.loading-indicator.hidden {
    display: none;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e9ecef;
    border-top: 2px solid #2196f3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 播放器底部 */
.player-footer {
    padding: 16px 24px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.progress-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.current-time,
.total-time {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

.progress-bar {
    flex: 1;
    height: 6px;
    background: #e9ecef;
    border-radius: 3px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #2196f3, #21cbf3);
    border-radius: 3px;
    transition: width 0.1s ease;
    width: 0%;
}

.progress-bar:hover .progress-fill {
    background: linear-gradient(90deg, #1976d2, #1cb5e0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .enhanced-audio-player {
        margin: 0;
        border-radius: 0;
    }
    
    .player-main {
        flex-direction: column;
    }
    
    .chapters-panel {
        width: 100%;
        max-height: 300px;
    }
    
    .chapters-panel.hidden {
        max-height: 0;
        width: 100%;
    }
    
    .chapters-list {
        height: 250px;
    }
    
    .player-area {
        padding: 16px;
    }
    
    .playback-controls {
        justify-content: center;
    }
    
    .playback-speed {
        margin-left: 0;
        margin-top: 8px;
    }
    
    .chapter-meta {
        flex-wrap: wrap;
        gap: 8px;
    }
}

@media (max-width: 480px) {
    .player-header {
        padding: 16px;
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }
    
    .book-info h3 {
        font-size: 1.25rem;
    }
    
    .chapter-item {
        padding: 10px 16px;
    }
    
    .chapter-name {
        font-size: 0.9rem;
    }
    
    .playback-controls button {
        min-width: 44px;
        height: 44px;
        padding: 10px;
    }
    
    .progress-info {
        gap: 8px;
    }
    
    .current-time,
    .total-time {
        min-width: 35px;
        font-size: 0.8rem;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .enhanced-audio-player {
        background: #1a1a1a;
        color: #ffffff;
    }
    
    .chapters-panel {
        background: #2d2d2d;
        border-right-color: #404040;
    }
    
    .chapters-header {
        background: #404040;
        border-bottom-color: #555555;
    }
    
    .chapters-header h4 {
        color: #ffffff;
    }
    
    .chapters-count {
        color: #cccccc;
    }
    
    .chapter-item:hover {
        background: #404040;
    }
    
    .chapter-item.active {
        background: #1e3a5f;
    }
    
    .chapter-name {
        color: #ffffff;
    }
    
    .chapter-details {
        color: #cccccc;
    }
    
    .chapter-title {
        color: #ffffff;
    }
    
    .chapter-meta {
        color: #cccccc;
    }
    
    .playback-controls button {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .playback-controls button:hover:not(:disabled) {
        background: #404040;
        border-color: #555555;
    }
    
    .speed-selector {
        background: #2d2d2d;
        border-color: #404040;
        color: #ffffff;
    }
    
    .player-footer {
        background: #2d2d2d;
        border-top-color: #404040;
    }
    
    .progress-bar {
        background: #404040;
    }
    
    .current-time,
    .total-time {
        color: #cccccc;
    }
}

/* 动画效果 */
.chapter-item {
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.enhanced-audio-player {
    animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 焦点样式 */
.playback-controls button:focus,
.speed-selector:focus,
.btn-toggle-chapters:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* 选择状态 */
.chapter-item:focus {
    outline: 2px solid #2196f3;
    outline-offset: -2px;
}

/* 工具提示样式 */
[title] {
    position: relative;
}

/* 滚动条美化 */
.chapters-list {
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .enhanced-audio-player {
        border: 2px solid #000000;
    }
    
    .chapter-item.active {
        border-left-width: 5px;
    }
    
    .progress-fill {
        background: #000000;
    }
    
    .btn-play-pause {
        background: #000000 !important;
        border-color: #000000 !important;
    }
} 