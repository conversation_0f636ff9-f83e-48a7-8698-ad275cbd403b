# Fix configuration issues

Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "   Configuration Fix Script" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

# Check if virtual environment is activated
Write-Host "[1/4] Checking virtual environment..." -ForegroundColor Yellow
if ($env:VIRTUAL_ENV -or (Test-Path "venv\Scripts\activate.bat")) {
    if (-not $env:VIRTUAL_ENV) {
        Write-Host "      Activating virtual environment..." -ForegroundColor Cyan
        try {
            & "venv\Scripts\Activate.ps1"
            Write-Host "      ✓ Virtual environment activated" -ForegroundColor Green
        } catch {
            Write-Host "      ⚠ PowerShell activation failed, trying batch..." -ForegroundColor Yellow
            cmd /c "venv\Scripts\activate.bat && echo Virtual environment activated"
        }
    } else {
        Write-Host "      ✓ Virtual environment already active" -ForegroundColor Green
    }
} else {
    Write-Host "      ✗ Virtual environment not found" -ForegroundColor Red
    Write-Host "      Please run the startup script first" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Test configuration loading
Write-Host ""
Write-Host "[2/4] Testing configuration..." -ForegroundColor Yellow
Write-Host "      Running configuration test..." -ForegroundColor Cyan

$testOutput = python test_config.py 2>&1
$testExitCode = $LASTEXITCODE

if ($testExitCode -eq 0) {
    Write-Host "      ✓ Configuration test passed" -ForegroundColor Green
} else {
    Write-Host "      ✗ Configuration test failed" -ForegroundColor Red
    Write-Host "      Output:" -ForegroundColor Yellow
    Write-Host $testOutput -ForegroundColor Red
    
    Write-Host ""
    Write-Host "      Attempting to fix configuration issues..." -ForegroundColor Cyan
    
    # Check if .env file exists
    if (-not (Test-Path ".env")) {
        Write-Host "      Creating .env file..." -ForegroundColor Cyan
        if (Test-Path ".env.example") {
            Copy-Item ".env.example" ".env"
            Write-Host "      ✓ Created .env from template" -ForegroundColor Green
        } else {
            Write-Host "      ✗ .env.example not found" -ForegroundColor Red
        }
    }
}

# Create necessary directories
Write-Host ""
Write-Host "[3/4] Creating necessary directories..." -ForegroundColor Yellow
$directories = @("data", "data\database", "data\uploads", "data\audio", "data\temp", "data\logs")

foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir -Force | Out-Null
        Write-Host "      ✓ Created directory: $dir" -ForegroundColor Green
    } else {
        Write-Host "      ✓ Directory exists: $dir" -ForegroundColor Gray
    }
}

# Test application startup
Write-Host ""
Write-Host "[4/4] Testing application startup..." -ForegroundColor Yellow
Write-Host "      Running quick startup test..." -ForegroundColor Cyan

# Create a simple test script
$testScript = @"
import sys
import os
from pathlib import Path

# Add app directory to Python path
app_dir = Path(__file__).parent
sys.path.insert(0, str(app_dir))

try:
    from app.core.config import settings
    print("✓ Configuration loaded successfully")
    print(f"  App: {settings.app_name}")
    print(f"  Host: {settings.host}:{settings.port}")

    from app.core.database import engine
    from sqlalchemy import text
    with engine.connect() as conn:
        conn.execute(text("SELECT 1"))
    print("✓ Database connection successful")
    
    print("✓ Application should start successfully")
    exit(0)
    
except Exception as e:
    print(f"✗ Error: {e}")
    exit(1)
"@

$testScript | Out-File -FilePath "quick_test.py" -Encoding UTF8

$quickTestOutput = python quick_test.py 2>&1
$quickTestExitCode = $LASTEXITCODE

# Clean up test file
Remove-Item "quick_test.py" -ErrorAction SilentlyContinue

if ($quickTestExitCode -eq 0) {
    Write-Host "      ✓ Quick startup test passed" -ForegroundColor Green
    Write-Host $quickTestOutput -ForegroundColor Green
} else {
    Write-Host "      ✗ Quick startup test failed" -ForegroundColor Red
    Write-Host $quickTestOutput -ForegroundColor Red
}

# Final status and recommendations
Write-Host ""
Write-Host "================================================" -ForegroundColor Cyan
Write-Host "   Fix Complete!" -ForegroundColor Cyan
Write-Host "================================================" -ForegroundColor Cyan
Write-Host ""

if ($quickTestExitCode -eq 0) {
    Write-Host "✅ Configuration issues have been resolved!" -ForegroundColor Green
    Write-Host ""
    Write-Host "You can now start the application with:" -ForegroundColor White
    Write-Host "  python main.py" -ForegroundColor Gray
    Write-Host ""
    Write-Host "Or use the startup script:" -ForegroundColor White
    Write-Host "  powershell -ExecutionPolicy Bypass -File start.ps1" -ForegroundColor Gray
    Write-Host ""
    
    $startNow = Read-Host "Start the application now? (y/N)"
    if ($startNow -eq "y" -or $startNow -eq "Y") {
        Write-Host ""
        Write-Host "Starting application..." -ForegroundColor Cyan
        Write-Host "Application URL: http://localhost:8000" -ForegroundColor Cyan
        Write-Host "Press Ctrl+C to stop" -ForegroundColor Yellow
        Write-Host ""
        python main.py
    }
} else {
    Write-Host "❌ Configuration issues persist" -ForegroundColor Red
    Write-Host ""
    Write-Host "Please check the following:" -ForegroundColor Yellow
    Write-Host "1. Ensure all required packages are installed" -ForegroundColor White
    Write-Host "2. Check .env file configuration" -ForegroundColor White
    Write-Host "3. Verify Python version compatibility" -ForegroundColor White
    Write-Host "4. Run the diagnostic script for more details:" -ForegroundColor White
    Write-Host "   powershell -ExecutionPolicy Bypass -File diagnose_python.ps1" -ForegroundColor Gray
}

Write-Host ""
Read-Host "Press Enter to exit"
